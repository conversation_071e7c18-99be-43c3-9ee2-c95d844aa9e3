# Arranque mínimo en Colab/Jupyter
import numpy as np
import pandas as pd

# Mejorar la visualización
pd.set_option("display.max_columns", 80)
pd.set_option("display.width", 120)

# Semilla para reproducibilidad
RANDOM_SEED = 42
rng = np.random.default_rng(RANDOM_SEED)

# Carga del dataset sintético principal (ruta de ejemplo)
df = pd.read_csv("data/hospital_general_sim.csv")

# Vista rápida
print(df.shape)     #(n_filas, n_columnas)
print(df.head(3))   #primeras filas
print(df.info())    #tipos de datos y nulos
print(df['readmit_30d'].value_counts(normalize=True))

# Comprobaciones iniciales sugeridas
# 1) Rangos fisiológicos básicos (ejemplo)
ok_glucose = df['glucose_mg_dl'].between(40, 600) | df['glucose_mg_dl'].isna()
print('Valores de glucosa fuera de rango:', (~ok_glucose).sum())

# 2) Ausentes por columna (proporción)
missing = df.isna().mean().sort_values(ascending=False)
print(missing.head(10))

# 3) Definir outcome y lista tentativa de predictores
TARGET = 'readmit_30d'
features = [c for c in df.columns if c not in {TARGET, 'patient_id', 'episode_id'}]
