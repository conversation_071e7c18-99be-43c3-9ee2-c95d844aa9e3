{"cells": [{"cell_type": "code", "execution_count": null, "id": "d407dbb9", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n"]}, {"cell_type": "code", "execution_count": null, "id": "892a4d98", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.preprocessing import StandardScaler, OneHotEncoder\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.linear_model import LogisticRegression\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "9218893b", "metadata": {}, "outputs": [], "source": ["df=pd.read_csv(\"base03.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "ed57385d", "metadata": {}, "outputs": [], "source": ["TARGET = 'readmit_30d'\n", "y= df [TARGET].astype(int)\n", "X= df.drop(columns=[TARGET])"]}, {"cell_type": "code", "execution_count": null, "id": "d2046332", "metadata": {}, "outputs": [], "source": ["# SEPARAR TIPSOS DE CLUMNAS\n", "num_cols = X.select_dtypes(include='number').columns.tolist()\n", "cat_cols = X.columns.difference(num_cols).tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "5e3f4c4f", "metadata": {}, "outputs": [], "source": ["print(num_cols)\n"]}, {"cell_type": "code", "execution_count": null, "id": "37612592", "metadata": {}, "outputs": [], "source": ["print(cat_cols)"]}, {"cell_type": "code", "execution_count": null, "id": "b01beb3c", "metadata": {}, "outputs": [], "source": ["# Preprocesadores\n", "num_prepro = Pipeline(steps=[\n", "    ('imputer', SimpleImputer(strategy='median')),\n", "    ('scaler', StandardScaler())\n", "])"]}, {"cell_type": "code", "execution_count": null, "id": "63d76ffc", "metadata": {}, "outputs": [], "source": ["cal_pre = Pipeline(steps=[\n", "    ('imputer', SimpleImputer(strategy='most_frequent')),\n", "    ('onehot', OneHotEncoder(handle_unknown='ignore'))\n", "])"]}, {"cell_type": "code", "execution_count": null, "id": "714b5076", "metadata": {}, "outputs": [], "source": ["prep=ColumnTransformer(transformers=[\n", "    ('num', num_pre, num_cols),\n", "    ('cat', cat_pre, cat_cols)\n", "])"]}, {"cell_type": "code", "execution_count": null, "id": "4c022c98", "metadata": {}, "outputs": [], "source": ["clf= Pipeline(steps=[\n", "    ('prep', prep),\n", "    ('model', LogisticRegression(max_iter=1000))\n", "])"]}, {"cell_type": "code", "execution_count": null, "id": "79c271e6", "metadata": {}, "outputs": [], "source": ["X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)\n"]}, {"cell_type": "code", "execution_count": null, "id": "5e0b3cff", "metadata": {}, "outputs": [], "source": ["\n", "scores = cross_val_score(clf, X_train, y_train, cv=5, scoring='roc_auc')\n", "print(\"AUC CV media: \", scores.mean())"]}, {"cell_type": "code", "execution_count": null, "id": "fa857e85", "metadata": {}, "outputs": [], "source": ["clf.fit(X_train, y_train)\n", "from sklearn.metrics import roc_auc_score, average_precision_score\n"]}, {"cell_type": "code", "execution_count": null, "id": "ce7fbebe", "metadata": {}, "outputs": [], "source": ["proba_test = clf.predict_proba(X_test)[:,1]\n", "print(\"ROC AUC (test): \", roc_auc_score(y_test, proba_test))\n", "print(\"PR AUC (test): \", average_precision_score(y_test, proba_test))"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}