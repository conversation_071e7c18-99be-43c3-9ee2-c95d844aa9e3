{"cells": [{"cell_type": "code", "execution_count": 1, "id": "eaa85e57-42ab-4432-9b43-8bd50b802478", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "df=pd.read_csv(\"HOSPITAL_General_Sim_min.csv\")"]}, {"cell_type": "code", "execution_count": 2, "id": "f60876fb-c13e-4b27-808c-72b508322317", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 200 entries, 0 to 199\n", "Data columns (total 19 columns):\n", " #   Column               Non-Null Count  Dtype  \n", "---  ------               --------------  -----  \n", " 0   episode_id           200 non-null    object \n", " 1   patient_id           200 non-null    object \n", " 2   admission_datetime   200 non-null    object \n", " 3   discharge_datetime   200 non-null    object \n", " 4   length_of_stay_days  200 non-null    float64\n", " 5   age_years            200 non-null    float64\n", " 6   sex                  200 non-null    object \n", " 7   bmi                  200 non-null    float64\n", " 8   hr_bpm               200 non-null    float64\n", " 9   sbp_mmhg             200 non-null    float64\n", " 10  spo2_pct             200 non-null    float64\n", " 11  temp_c               200 non-null    float64\n", " 12  hb_g_dl              200 non-null    float64\n", " 13  creatinine_mg_dl     195 non-null    float64\n", " 14  glucose_mg_dl        200 non-null    float64\n", " 15  wbc_10e9_per_l       190 non-null    float64\n", " 16  egfr_ml_min_1_73m2   200 non-null    float64\n", " 17  edad_band            200 non-null    object \n", " 18  readmit_30d          200 non-null    int64  \n", "dtypes: float64(12), int64(1), object(6)\n", "memory usage: 29.8+ KB\n"]}], "source": ["# Inspeccion o ojada a las primeras columnas\n", "df.head(3)\n", "df.info()"]}, {"cell_type": "code", "execution_count": 3, "id": "d63012d7-a733-4330-8e16-236cd3be140c", "metadata": {}, "outputs": [], "source": ["# Seleccion de columnas de interes y filtrado por adultos\n", "cols = ['age_years','sex','bmi','hr_bpm','sbp_mmhg','spo2_pct','hb_g_dl','creatinine_mg_dl','glucose_mg_dl','readmit_30d']\n", "adultos =df.loc[df['age_years'] >= 18, cols].copy()"]}, {"cell_type": "code", "execution_count": 4, "id": "242f25dd-62a5-44bb-afdc-b136df0cfaf4", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     age_years sex   bmi  hr_bpm  sbp_mmhg  spo2_pct  hb_g_dl  \\\n", "0         78.3   M  23.1    80.0     136.0      96.0     11.9   \n", "1         71.5   F  27.3    71.0     113.0      93.0     12.1   \n", "2         66.1   M  25.9   110.0     114.0      96.0     12.7   \n", "3         46.3   F  24.2    59.0     141.0      98.0     12.0   \n", "4         60.8   F  25.0    78.0     117.0      97.0     13.0   \n", "..         ...  ..   ...     ...       ...       ...      ...   \n", "195       46.6   M  19.6    81.0     115.0      96.0     10.3   \n", "196       50.4   M  26.9    57.0     115.0      95.0     12.6   \n", "197       57.1   M  28.9    71.0     104.0      91.0     12.9   \n", "198       31.0   F  36.0   103.0     125.0      98.0     10.9   \n", "199       52.0   F  26.6    74.0      95.0      92.0     13.5   \n", "\n", "     creatinine_mg_dl  glucose_mg_dl  readmit_30d  \n", "0            1.742742      50.000000            0  \n", "1            2.066113     132.798301            0  \n", "2            1.026788     146.133364            0  \n", "3            1.576398     180.157610            0  \n", "4            0.969788     145.695530            0  \n", "..                ...            ...          ...  \n", "195          1.133830     167.408749            0  \n", "196          1.248888     165.704117            0  \n", "197          1.353258      82.830951            0  \n", "198          0.830796      92.191500            0  \n", "199          1.360353      81.339052            0  \n", "\n", "[200 rows x 10 columns]\n"]}], "source": ["print(adultos)"]}, {"cell_type": "code", "execution_count": 5, "id": "d9081ee0-652e-46a9-8f7a-d13a13803856", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     age_years sex   bmi  hr_bpm  sbp_mmhg  spo2_pct  hb_g_dl  \\\n", "0         78.3   M  23.1    80.0     136.0      96.0     11.9   \n", "1         71.5   F  27.3    71.0     113.0      93.0     12.1   \n", "2         66.1   M  25.9   110.0     114.0      96.0     12.7   \n", "3         46.3   F  24.2    59.0     141.0      98.0     12.0   \n", "4         60.8   F  25.0    78.0     117.0      97.0     13.0   \n", "..         ...  ..   ...     ...       ...       ...      ...   \n", "195       46.6   M  19.6    81.0     115.0      96.0     10.3   \n", "196       50.4   M  26.9    57.0     115.0      95.0     12.6   \n", "197       57.1   M  28.9    71.0     104.0      91.0     12.9   \n", "198       31.0   F  36.0   103.0     125.0      98.0     10.9   \n", "199       52.0   F  26.6    74.0      95.0      92.0     13.5   \n", "\n", "     creatinine_mg_dl  glucose_mg_dl  readmit_30d  \n", "0            1.742742      50.000000            0  \n", "1            2.066113     132.798301            0  \n", "2            1.026788     146.133364            0  \n", "3            1.576398     180.157610            0  \n", "4            0.969788     145.695530            0  \n", "..                ...            ...          ...  \n", "195          1.133830     167.408749            0  \n", "196          1.248888     165.704117            0  \n", "197          1.353258      82.830951            0  \n", "198          0.830796      92.191500            0  \n", "199          1.360353      81.339052            0  \n", "\n", "[200 rows x 10 columns]\n"]}], "source": ["# tipo sexo como categoria ordenada\n", "adultos['sex'] = adultos['sex'].astype('category')\n", "\n", "print(adultos)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "35d75a31-08b6-4e58-9ea5-1bdb5fa9b609", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25088\\2091251972.py:10: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  .groupby('edad_band')\n"]}], "source": ["# resumenes por banda de edad\n", "def edad_band(x):\n", "    if pd.isna(x): return pd.NA\n", "    if x < 40: return '<40'\n", "    if x < 60: return '40-59'\n", "    if x < 75: return '60-74'\n", "    return '>=75'\n", "adultos['edad_band'] = adultos['age_years'].map(edad_band).astype('category')\n", "tabla = (adultos\n", "    .groupby('edad_band')\n", "    .agg(n=('readmit_30d','size'),\n", "         reingreso_media=('readmit_30d','mean'),\n", "         imc_median=('bmi','median'),\n", "         hb_med=('hb_g_dl','mean'))\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": 7, "id": "88b3b7e3-6b8e-495f-8a6e-db2c2cc1cb51", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  edad_band   n  reingreso_media  imc_median     hb_med\n", "3      >=75  39         0.230769       26.60  11.951282\n", "1     60-74  66         0.196970       26.50  12.272727\n", "0     40-59  83         0.156627       27.30  12.293976\n", "2       <40  12         0.083333       26.65  12.191667\n"]}], "source": ["print(tabla.sort_values('reingreso_media', ascending=False))"]}, {"cell_type": "code", "execution_count": 8, "id": "e355fa50-a988-491d-a2f4-13df38073091", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  edad_band   n  reingreso_media  imc_median     hb_med riesgo_base\n", "0     40-59  83         0.156627       27.30  12.293976  medio-bajo\n", "1     60-74  66         0.196970       26.50  12.272727  medio-alto\n", "2       <40  12         0.083333       26.65  12.191667        bajo\n", "3      >=75  39         0.230769       26.60  11.951282        alto\n"]}], "source": ["# Enriquecer con diccionarios \n", "mapa_band = pd.DataFrame({'edad_band': ['<40','40-59','60-74','>=75'],'riesgo_base':['bajo','medio-bajo','medio-alto','alto']})\n", "tabla= tabla.merge(mapa_band, on='edad_band', how='left')\n", "print(tabla)"]}, {"cell_type": "code", "execution_count": 9, "id": "a295d2ab-a943-44c6-b693-eacdcf1e59d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Datos guardados en S03TablaResumen.csv\n"]}], "source": ["# guardar resultados intermedios\n", "tabla.to_csv(\"S03TablaResumen.csv\", index=False)\n", "print(\"Datos guardados en S03TablaResumen.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "cadb6ab7-5174-4141-aa23-0a1c52629ad7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}