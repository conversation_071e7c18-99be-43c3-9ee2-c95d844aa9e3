{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7c1cdcbf-e621-4128-809f-0653dbc67107", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "id": "def5b675-5a46-48d2-9e2f-6902df67b784", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.linear_model import LogisticRegression"]}, {"cell_type": "code", "execution_count": 3, "id": "8ad214cf-1bbb-40d1-8942-04e1aca6cf46", "metadata": {}, "outputs": [], "source": ["df=pd.read_csv(\"baseS03.csv\")"]}, {"cell_type": "code", "execution_count": 4, "id": "6b085930-939e-49ad-8a58-73c9102322e7", "metadata": {}, "outputs": [], "source": ["TARGET = 'readmit_30d'\n", "y= df[TARGET].astype(int)\n", "X = df.drop(columns =[TARGET])"]}, {"cell_type": "code", "execution_count": 5, "id": "85f71f2b-c5ba-4ba9-b755-a705afaaa2ff", "metadata": {}, "outputs": [], "source": ["# separar tipos de columnas\n", "num_cols= X.select_dtypes(include='number').columns.tolist()\n", "cat_cols= X.columns.difference(num_cols).tolist()"]}, {"cell_type": "code", "execution_count": 6, "id": "69e807f1-8ee5-4a7c-99df-a6958f73e42d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['age_years', 'bmi', 'hr_bpm', 'sbp_mmhg', 'spo2_pct', 'hb_g_dl', 'creatinine_mg_dl', 'glucose_mg_dl']\n"]}], "source": ["print(num_cols)"]}, {"cell_type": "code", "execution_count": 7, "id": "24873bcc-ee4b-481f-b94d-c3c93167eaf7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['edad_band', 'sex', 'taquicardia']\n"]}], "source": ["print(cat_cols)"]}, {"cell_type": "code", "execution_count": 8, "id": "6478781c-6247-4078-8a04-fbd48dd4760a", "metadata": {}, "outputs": [], "source": ["# Preprocesadores \n", "num_pre = Pipeline(steps=[('imputer',SimpleImputer(strategy='median')),('scaler',StandardScaler())])\n"]}, {"cell_type": "code", "execution_count": 9, "id": "889e3db5-fa7b-48c0-93f9-6814e5ad484e", "metadata": {}, "outputs": [], "source": ["cat_pre = Pipeline(steps=[('imputer',SimpleImputer(strategy='most_frequent')),\n", "                          ('onehot', OneHotEncoder(handle_unknown='ignore'))])"]}, {"cell_type": "code", "execution_count": 10, "id": "65790534-63d0-49c7-9953-1a6ac4b43f38", "metadata": {}, "outputs": [], "source": ["prep=ColumnTransformer(transformers=[('num', num_pre, num_cols),('cat', cat_pre, cat_cols)])"]}, {"cell_type": "code", "execution_count": 11, "id": "c200a7b5-50ee-4d91-b591-440f57720d0c", "metadata": {}, "outputs": [], "source": ["clf= Pipeline(steps=[('prep',prep),('model', LogisticRegression(max_iter=1000))])\n"]}, {"cell_type": "code", "execution_count": 12, "id": "1b3ec2fb-4364-4283-82e9-f2fdac0d0c00", "metadata": {}, "outputs": [], "source": ["X_train, X_text, y_train, y_test = train_test_split(X, y, test_size =0.2, random_state=42, stratify=y)"]}, {"cell_type": "code", "execution_count": 13, "id": "4509c3cc-fef4-4d19-bf14-cc39e7dcf0ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AUC CV media:  0.5724786324786325\n"]}], "source": ["scores = cross_val_score(clf, X_train, y_train, cv=5, scoring='roc_auc')\n", "print(\"AUC CV media: \", scores.mean())"]}, {"cell_type": "code", "execution_count": 14, "id": "fc07d239-df9c-4bba-8b03-fa60ecc94f7f", "metadata": {}, "outputs": [], "source": ["clf.fit(X_train, y_train)\n", "from sklearn.metrics import roc_auc_score, average_precision_score"]}, {"cell_type": "code", "execution_count": 15, "id": "277bcef5-676a-415f-90c8-c8d4fa292cdd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ROC AUC (test):  0.5238095238095237\n", "PR AUC (test):  0.2120362077258629\n"]}], "source": ["proba_test = clf.predict_proba(X_text)[:, 1]\n", "print(\"ROC AUC (test): \", roc_auc_score(y_test, proba_test))\n", "print(\"PR AUC (test): \", average_precision_score(y_test, proba_test))"]}, {"cell_type": "code", "execution_count": null, "id": "061e0111-31e2-4c22-99c0-6ed00bc7bf4a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}