{"cells": [{"cell_type": "code", "execution_count": 43, "id": "288de824-4823-424f-bb5d-a1f03864c326", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import re\n", "df = pd.read_csv(\"HOSPITAL_General_Sim_200.csv\")\n"]}, {"cell_type": "code", "execution_count": 44, "id": "acb01b8d-156c-4934-af65-176e4cb6d203", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Columnas sospechosas:  []\n"]}], "source": ["#Buscamos columnas sospechosas para asegurarnos que no tenemos columnas que incunplan la proteccion de datos\n", "suspect_cols = [c for c in df.columns if re.search(r'(name|surname|address|email|phone|nhc|dni)', c, flags=re.I)]\n", "print(\"Columnas sospechosas: \", suspect_cols)\n", "\n", "                                                 "]}, {"cell_type": "code", "execution_count": 45, "id": "28406e8f-b2e4-4a9b-bce1-6842813843fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Posibles columnas de texto libre:  []\n"]}], "source": ["#Recorremos todas las columnas para aplicar una heuristica de \"texto libre\"\n", "text_like = []\n", "for c in df.columns:\n", "    if df[c].dtype == 'object':\n", "        sample = df[c].dropna().astype(str).head(50)\n", "        if sample.map(len).mean() > 50: #si es mayor de 60 caracteres es largo\n", "            text_like.append(c)\n", "print(\"Posibles columnas de texto libre: \", text_like)"]}, {"cell_type": "code", "execution_count": 46, "id": "35328673-9e39-499c-a4fc-52cbe405b7a6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["d_dimer_ng_ml           0.305\n", "troponin_ng_l           0.280\n", "crp_mg_l                0.230\n", "bilirubin_mg_dl         0.110\n", "ast_u_l                 0.100\n", "alt_u_l                 0.095\n", "albumin_g_dl            0.080\n", "wbc_10e9_per_l          0.050\n", "creatinine_mg_dl        0.025\n", "platelets_10e9_per_l    0.010\n", "length_of_stay_days     0.000\n", "admit_source            0.000\n", "episode_id              0.000\n", "patient_id              0.000\n", "admission_type          0.000\n", "dtype: float64\n"]}], "source": ["# perfil de ausentes por columna\n", "missing = df.isna().mean().sort_values(ascending=False)\n", "print(missing.head(15))\n", "      "]}, {"cell_type": "code", "execution_count": 47, "id": "fba46302-ebaf-4691-830e-880fd0bd595a", "metadata": {}, "outputs": [], "source": ["# calcular \"Bandas\"(rangos) de Edad \n", "def edad_band(age):\n", "    if pd.isna(age):\n", "        return pd.NA\n", "    if age <40: return \"<40\"\n", "    if age <60: return \"40-59\"\n", "    if age <75: return \"60-74\"\n", "    return \">=75\"\n", "df['edad_band'] = df['age_years'].map(edad_band)\n"]}, {"cell_type": "code", "execution_count": 48, "id": "e37aee46-09db-412a-b4d3-61371fec0333", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0       >=75\n", "1      60-74\n", "2      60-74\n", "3      40-59\n", "4      60-74\n", "       ...  \n", "195    40-59\n", "196    40-59\n", "197    40-59\n", "198      <40\n", "199    40-59\n", "Name: edad_band, Length: 200, dtype: object\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>n</th>\n", "      <th>%</th>\n", "    </tr>\n", "    <tr>\n", "      <th>edad_band</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>&lt;40</th>\n", "      <td>12</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40-59</th>\n", "      <td>83</td>\n", "      <td>41.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60-74</th>\n", "      <td>66</td>\n", "      <td>33.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>&gt;=75</th>\n", "      <td>39</td>\n", "      <td>19.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            n     %\n", "edad_band          \n", "<40        12   6.0\n", "40-59      83  41.5\n", "60-74      66  33.0\n", ">=75       39  19.5"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["print(df['edad_band'])\n", "orden = ['<40','40-59','60-74','>=75']\n", "df['edad_band']=pd.Categorical(df['edad_band'], categories= orden, ordered=True)\n", "tabla = (df.loc[df['edad_band'].notna(), 'edad_band']\n", "    .value_counts()\n", "    .reindex(orden)\n", "    .rename('n')\n", "    .to_frame())\n", "tabla['%'] = (tabla['n'] / tabla['n'].sum() * 100).round(1)\n", "\n", "tabla"]}, {"cell_type": "code", "execution_count": 49, "id": "396aa30e-3f0b-4645-aa14-73991f0f6677", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0       40.8\n", "1       38.2\n", "2       68.5\n", "3       70.5\n", "4       74.1\n", "       ...  \n", "195     81.4\n", "196     75.5\n", "197     67.5\n", "198    101.4\n", "199     71.4\n", "Name: egfr_ml_min_1_73m2, Length: 200, dtype: float64\n"]}], "source": ["#eGFR aprox 100 - 1.5*(edad-40)\n", "\n", "if \"egfr_ml_min_1_73m2\" not in df.columns:\n", "    def egfr_ml_min_1_73m2(row):\n", "        if pd.isna(row['creatinine_mg_ml']) or pd.isna(row['age_years']):\n", "            return pd.NA\n", "        return 100 - 1.5*max(0, row['age_years']-40) - 25*max(0, row['cretinine_mg_ml']-1)\n", "    df['egfr_ml_min_1_73m2'] = df.apply(egfr_ml_min_1_73m2, axis=1)\n", "\n", "print(df.egfr_ml_min_1_73m2)\n"]}, {"cell_type": "code", "execution_count": 50, "id": "bb658f95-9b35-4e90-814c-238e5659d9a0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OK: HOSPITAL_General_Sim_min.csv generado\n"]}], "source": ["keep = [ \"episode_id\",\"patient_id\",\"admission_datetime\",\"discharge_datetime\",\"length_of_stay_days\",\n", "         \"age_years\",\"sex\",\"bmi\",\"hr_bpm\",\"sbp_mmhg\",\"spo2_pct\",\"temp_c\",\"hb_g_dl\",\"creatinine_mg_dl\",\n", "         \"glucose_mg_dl\",\"wbc_10e9_per_l\",\"egfr_ml_min_1_73m2\",\"edad_band\",\"readmit_30d\"]\n", "\n", "#guardaremos este keep en un archivos csv nuevo \n", "df[keep].to_csv(\"HOSPITAL_General_Sim_min.csv\", index=False) \n", "print(\"OK: HOSPITAL_General_Sim_min.csv generado\")"]}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}