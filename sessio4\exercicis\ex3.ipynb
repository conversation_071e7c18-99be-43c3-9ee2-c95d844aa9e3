{"cells": [{"cell_type": "code", "execution_count": 7, "id": "cb982eea", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.linear_model import LogisticRegression\n", "\n", "df = pd.read_csv(\"HOSPITAL_General_Sim_min.csv\")\n", "\n", "TARGET = 'readmit_30d'\n", "y = df[TARGET].astype(int)\n", "X = df.drop(columns=[TARGET])"]}, {"cell_type": "code", "execution_count": 8, "id": "6e477448", "metadata": {}, "outputs": [], "source": ["\n", "# Separar tipus de columnes\n", "num_cols = X.select_dtypes(include='number').columns.tolist()\n", "cat_cols = X.columns.difference(num_cols).tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "cd44e5f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AUC CV media: 0.483\n", "ROC AUC (test): 0.554\n", "PR AUC (test): 0.212\n"]}], "source": ["\n", "# Preprocessadors\n", "from sklearn.metrics import roc_auc_score, average_precision_score\n", "num_pre = Pipeline(steps=[\n", "    ('imputer', SimpleImputer(strategy='median')),\n", "    ('scaler', StandardScaler())\n", "])\n", "\n", "cat_pre = Pipeline(steps=[\n", "    ('imputer', SimpleImputer(strategy='most_frequent')),\n", "    ('onehot', OneHotEncoder(handle_unknown='ignore'))])\n", "\n", "prep = ColumnTransformer(\n", "    transformers=[\n", "        ('num', num_pre, num_cols),\n", "        ('cat', cat_pre, cat_cols)])\n", "\n", "clf = Pipeline(steps=[('prep', prep),\n", "                      ('model', LogisticRegression(max_iter=1000))])\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2,\n", "                                                    random_state=42, stratify=y)\n", "\n", "scores = cross_val_score(clf, X_train, y_train, cv=5, scoring='roc_auc')\n", "print(f\"AUC CV media: {scores.mean():.3f}\")  # pag 51 llibre\n", "\n", "clf.fit(X_train, y_train)\n", "\n", "proba_test = clf.predict_proba(X_test)[:, 1]\n", "print(f\"ROC AUC (test): {roc_auc_score(y_test, proba_test):.3f}\")\n", "print(f\"PR AUC (test): {average_precision_score(y_test, proba_test):.3f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "e0829074", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}