{"cells": [{"cell_type": "code", "execution_count": null, "id": "8735b15b-f54f-4553-b0f7-4eb31a1e81f5", "metadata": {}, "outputs": [], "source": ["#Para \"cargar o importar\" la libreria Pandas\n", "import pandas as pd\n", "import numpy as np\n", "\n", "#funciones tipicas de la libreria pandas\n", "#Entrada y salida de datos\n", "df=pd.read_csv    df.to_csv(\"nombre del archivo a guardar.csv\")\n", "\n", "#Reconocimiento del terreno, tipos, ausentes, y estadisticos\n", "head  info  describe\n", "\n", "#para crear columnas derivadas  \n", "assing\n", "\n", "# si las columnas tienen nombres largos o ilegibles \n", "rename\n", "\n", "# tipos coherentes o enteros seguros con NA\n", "astype\n", "\n", "#Resumenes por subgrupos clinicos\n", "groupby   +  agg\n", "\n", "#Enriquecimiento de diccionarios\n", "merge\n", "\n", "# ordenar para insepccione o seleccion\n", "sort_values\n", "\n", "#para encadenar pasos en un flujo legible\n", "pipe"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}