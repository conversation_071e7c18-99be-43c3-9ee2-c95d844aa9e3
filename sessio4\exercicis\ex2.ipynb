{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c706942f", "metadata": {"notebookRunGroups": {"groupValue": "1"}}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "# <PERSON>egim el dataset\n", "df = pd.read_csv(\"../exercicis/HOSPITAL_General_Sim_min.csv\")\n"]}, {"cell_type": "code", "execution_count": 2, "id": "b2562dbb", "metadata": {}, "outputs": [], "source": ["# Definim els rangs vàlids per cada variable\n", "rangos = {\n", "    \"glucose_mg_dl\": (40, 600),\n", "    \"sbp_mmhg\": (60, 260),\n", "    \"hr_bpm\": (30, 220),\n", "    \"temp_c\": (30, 43),\n", "}"]}, {"cell_type": "code", "execution_count": 3, "id": "d2996580", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["       glucose_mg_dl    sbp_mmhg      hr_bpm      temp_c\n", "count     200.000000  200.000000  200.000000  200.000000\n", "mean      124.460075  128.250000   82.315000   36.960000\n", "std        39.560821   16.344824   12.493849    0.590545\n", "min        50.000000   80.000000   51.000000   35.500000\n", "25%        90.098843  117.000000   75.000000   36.575000\n", "50%       125.349532  127.000000   83.500000   37.000000\n", "75%       153.886403  138.250000   90.000000   37.400000\n", "max       228.594106  173.000000  121.000000   38.800000\n"]}], "source": ["\n", "# Recorrem les variables i apliquem el control\n", "for col, (low, high) in rangos.items():\n", "    if col in df.columns:  # només si la columna existeix al dataset\n", "        # 1. Fora del rang → NaN\n", "        df.loc[(df[col] < low) | (df[col] > high), col] = np.nan\n", "        \n", "        # 2. Opcional: capar als lí<PERSON>s\n", "        df[col] = df[col].clip(lower=low, upper=high)\n", "\n", "# Confirmació: mirem un resum estadístic després del control\n", "print(df[rangos.keys()].describe())\n"]}, {"cell_type": "code", "execution_count": 4, "id": "f8df600e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame saved to figures/ex2.csv\n"]}], "source": ["import os\n", "\n", "os.makedirs(\"figures\", exist_ok=True)\n", "try:\n", "\tdf.to_csv(\"figures/ex2.csv\", index=False)\n", "\tprint(\"DataFrame saved to figures/ex2.csv\")\n", "except NameError:\n", "\tprint(\"Error: 'df' is not defined. Please run the cell that loads the dataframe.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "d81c5e4a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}