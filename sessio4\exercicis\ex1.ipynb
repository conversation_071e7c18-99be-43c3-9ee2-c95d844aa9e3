{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8afc64af", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                columna  propNA\n", "15       wbc_10e9_per_l   0.050\n", "13     creatinine_mg_dl   0.025\n", "0            episode_id   0.000\n", "10             spo2_pct   0.000\n", "17            edad_band   0.000\n", "16   egfr_ml_min_1_73m2   0.000\n", "14        glucose_mg_dl   0.000\n", "12              hb_g_dl   0.000\n", "11               temp_c   0.000\n", "9              sbp_mmhg   0.000\n", "1            patient_id   0.000\n", "8                hr_bpm   0.000\n", "7                   bmi   0.000\n", "6                   sex   0.000\n", "5             age_years   0.000\n", "4   length_of_stay_days   0.000\n", "3    discharge_datetime   0.000\n", "2    admission_datetime   0.000\n", "18          readmit_30d   0.000\n"]}], "source": ["import pandas as pd\n", "\n", "# 1. <PERSON><PERSON><PERSON> el dataset\n", "df = pd.read_csv(\"HOSPITAL_General_Sim_min.csv\")\n", "\n", "# 2. Calcular percentatge de valors buits per columna\n", "prop_na = df.isna().mean().reset_index()\n", "prop_na.columns = [\"columna\", \"propNA\"]\n", "\n", "# 3. <PERSON>den<PERSON> de més a menys\n", "prop_na = prop_na.sort_values(by=\"propNA\", ascending=False)\n", "\n", "# 4. Mostrar taula amb columna i propNA\n", "print(prop_na)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "11c1757d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  episode_id patient_id admission_datetime          discharge_datetime  \\\n", "0    E009954    P002051         2024-10-02  2024-10-05 21:37:40.502396   \n", "1    E003851    P001082         2022-11-23  2022-11-26 10:24:57.655017   \n", "2    E004963    P002561         2022-08-15  2022-08-19 05:03:13.008710   \n", "3    E003887    P000977         2025-01-30  2025-02-05 00:13:01.992119   \n", "4    E005438    P002789         2025-02-15  2025-02-22 01:15:30.595644   \n", "\n", "   length_of_stay_days  age_years sex   bmi  hr_bpm  sbp_mmhg  ...  hb_g_dl  \\\n", "0                 3.90       78.3   M  23.1    80.0     136.0  ...     11.9   \n", "1                 3.43       71.5   F  27.3    71.0     113.0  ...     12.1   \n", "2                 4.21       66.1   M  25.9   110.0     114.0  ...     12.7   \n", "3                 6.01       46.3   F  24.2    59.0     141.0  ...     12.0   \n", "4                 7.05       60.8   F  25.0    78.0     117.0  ...     13.0   \n", "\n", "   creatinine_mg_dl  glucose_mg_dl  wbc_10e9_per_l  egfr_ml_min_1_73m2  \\\n", "0          1.742742      50.000000        5.970629                40.8   \n", "1          2.066113     132.798301        9.859175                38.2   \n", "2          1.026788     146.133364             NaN                68.5   \n", "3          1.576398     180.157610       10.222832                70.5   \n", "4          0.969788     145.695530       10.525624                74.1   \n", "\n", "   edad_band  readmit_30d wbc_10e9_per_l_missing  creatinine_mg_dl_missing  \\\n", "0       >=75            0                      0                         0   \n", "1      60-74            0                      0                         0   \n", "2      60-74            0                      1                         0   \n", "3      40-59            0                      0                         0   \n", "4      60-74            0                      0                         0   \n", "\n", "   episode_id_missing  \n", "0                   0  \n", "1                   0  \n", "2                   0  \n", "3                   0  \n", "4                   0  \n", "\n", "[5 rows x 22 columns]\n"]}], "source": ["# 5. <PERSON><PERSON><PERSON> les 3 columnes amb més buits\n", "top3_cols = prop_na.head(3)[\"columna\"].tolist()\n", "\n", "# 6. <PERSON><PERSON><PERSON> <PERSON>es bandera per aquestes 3\n", "for col in top3_cols:\n", "    df[f\"{col}_missing\"] = df[col].isna().astype(int)\n", "\n", "# 7. Confirma<PERSON> que les banderes existeixen\n", "print(df.head())\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f1391241", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON><PERSON> creades: ['wbc_10e9_per_l_missing', 'creatinine_mg_dl_missing', 'episode_id_missing']\n"]}], "source": ["print(\"Banderes creades:\", [f\"{c}_missing\" for c in top3_cols])\n"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}