Stack trace:
Frame         Function      Args
0007FFFF9960  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8860) msys-2.0.dll+0x2118E
0007FFFF9960  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFF9960  0002100469F2 (00021028DF99, 0007FFFF9818, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9960  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9960  00021006A545 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF75DA0000 ntdll.dll
7FFF75060000 KERNEL32.DLL
7FFF73680000 KERNELBASE.dll
7FFF74890000 USER32.dll
7FFF73B40000 win32u.dll
7FFF73C30000 GDI32.dll
7FFF734A0000 gdi32full.dll
7FFF735D0000 msvcp_win.dll
7FFF730A0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF73E20000 advapi32.dll
7FFF73B80000 msvcrt.dll
7FFF741A0000 sechost.dll
7FFF74A60000 RPCRT4.dll
7FFF72550000 CRYPTBASE.DLL
7FFF73400000 bcryptPrimitives.dll
7FFF74160000 IMM32.DLL
